page-a5b941cb90d4693a.js:1 Pattern changed: {type: 'weekly', interval: 1, daysOfWeek: Array(5)}
page-a5b941cb90d4693a.js:1 
            
            
           POST http://localhost:3000/api/dentist/schedule/templates 400 (Bad Request)
(anonymous) @ page-a5b941cb90d4693a.js:1
onClick @ page-a5b941cb90d4693a.js:1
iX @ 4bd1b696-f495732fc8ec87ec.js:1
(anonymous) @ 4bd1b696-f495732fc8ec87ec.js:1
nS @ 4bd1b696-f495732fc8ec87ec.js:1
i2 @ 4bd1b696-f495732fc8ec87ec.js:1
s7 @ 4bd1b696-f495732fc8ec87ec.js:1
s5 @ 4bd1b696-f495732fc8ec87ec.js:1
1684-9bd53adb6b17a5e0.js:1 Failed to save pattern: Error: Invalid input data
    at Object.createTemplate (page-a5b941cb90d4693a.js:1:53166)
    at async onClick (page-a5b941cb90d4693a.js:1:64595)
overrideMethod @ hook.js:608
window.console.error @ 1684-9bd53adb6b17a5e0.js:1
onClick @ page-a5b941cb90d4693a.js:1
await in onClick
iX @ 4bd1b696-f495732fc8ec87ec.js:1
(anonymous) @ 4bd1b696-f495732fc8ec87ec.js:1
nS @ 4bd1b696-f495732fc8ec87ec.js:1
i2 @ 4bd1b696-f495732fc8ec87ec.js:1
s7 @ 4bd1b696-f495732fc8ec87ec.js:1
s5 @ 4bd1b696-f495732fc8ec87ec.js:1
page-a5b941cb90d4693a.js:1 Pattern changed: {type: 'weekly', interval: 1, daysOfWeek: Array(5)}
