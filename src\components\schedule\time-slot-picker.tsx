'use client'

import * as React from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { cn } from '@/lib/utils'
import { Clock, Plus, Minus, Loader2, Save, X } from 'lucide-react'
import { timeSlotSchema, type TimeSlotFormData } from '@/lib/validations/schedule'
import { toast } from 'sonner'

interface TimeSlot {
  startTime: string
  endTime: string
  title?: string
  description?: string
}

interface TimeSlotPickerProps {
  value?: TimeSlot
  onChange?: (timeSlot: TimeSlot) => void
  onSave?: (timeSlot: TimeSlotFormData) => Promise<void>
  onCancel?: () => void
  className?: string
  title?: string
  description?: string
  allowCustomTitle?: boolean
  minTime?: string
  maxTime?: string
  step?: number // in minutes
  loading?: boolean
  showActions?: boolean
  mode?: 'inline' | 'form' // inline for simple picker, form for full form with validation
}

export function TimeSlotPicker({
  value,
  onChange,
  onSave,
  onCancel,
  className,
  title = "Select Time Slot",
  description = "Choose start and end times",
  allowCustomTitle = false,
  minTime = "06:00",
  maxTime = "22:00",
  step = 30,
  loading = false,
  showActions = false,
  mode = 'inline'
}: TimeSlotPickerProps) {
  // Form setup with validation
  const form = useForm<TimeSlotFormData>({
    resolver: zodResolver(timeSlotSchema),
    defaultValues: {
      startTime: value?.startTime || "09:00",
      endTime: value?.endTime || "17:00",
      title: value?.title || "",
      description: value?.description || ""
    }
  })

  // Update form when value prop changes
  React.useEffect(() => {
    if (value) {
      form.reset({
        startTime: value.startTime,
        endTime: value.endTime,
        title: value.title || "",
        description: value.description || ""
      })
    }
  }, [value, form])

  // Legacy state for inline mode
  const [startTime, setStartTime] = React.useState(value?.startTime || "09:00")
  const [endTime, setEndTime] = React.useState(value?.endTime || "17:00")
  const [slotTitle, setSlotTitle] = React.useState(value?.title || "")

  // Generate time options based on step
  const generateTimeOptions = () => {
    const options = []
    const [minHour, minMinute] = minTime.split(':').map(Number)
    const [maxHour, maxMinute] = maxTime.split(':').map(Number)
    
    const startMinutes = minHour * 60 + minMinute
    const endMinutes = maxHour * 60 + maxMinute
    
    for (let minutes = startMinutes; minutes <= endMinutes; minutes += step) {
      const hour = Math.floor(minutes / 60)
      const minute = minutes % 60
      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
      options.push(timeString)
    }
    
    return options
  }

  const timeOptions = generateTimeOptions()

  // Form submission handler
  const onSubmit = async (data: TimeSlotFormData) => {
    if (onSave) {
      try {
        await onSave(data)
        toast.success('Time slot saved successfully')
        form.reset()
      } catch {
        toast.error('Failed to save time slot')
      }
    }
  }

  // Handle cancel action
  const handleCancel = () => {
    form.reset()
    if (onCancel) {
      onCancel()
    }
  }

  // Update parent when values change (inline mode)
  React.useEffect(() => {
    if (onChange && mode === 'inline') {
      onChange({
        startTime,
        endTime,
        title: slotTitle
      })
    }
  }, [startTime, endTime, slotTitle, onChange, mode])

  // Watch form values for real-time validation feedback
  const watchedValues = form.watch()

  // Validate that end time is after start time
  const isValidTimeRange = () => {
    const [startHour, startMinute] = startTime.split(':').map(Number)
    const [endHour, endMinute] = endTime.split(':').map(Number)
    
    const startMinutes = startHour * 60 + startMinute
    const endMinutes = endHour * 60 + endMinute
    
    return endMinutes > startMinutes
  }

  // Calculate duration
  const getDuration = () => {
    if (!isValidTimeRange()) return 0
    
    const [startHour, startMinute] = startTime.split(':').map(Number)
    const [endHour, endMinute] = endTime.split(':').map(Number)
    
    const startMinutes = startHour * 60 + startMinute
    const endMinutes = endHour * 60 + endMinute
    
    return endMinutes - startMinutes
  }

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    
    if (hours === 0) return `${mins}m`
    if (mins === 0) return `${hours}h`
    return `${hours}h ${mins}m`
  }

  // Quick time adjustment functions
  const adjustTime = (timeType: 'start' | 'end', adjustment: number) => {
    const currentTime = timeType === 'start' ? startTime : endTime
    const [hour, minute] = currentTime.split(':').map(Number)
    const currentMinutes = hour * 60 + minute
    const newMinutes = Math.max(0, Math.min(24 * 60 - step, currentMinutes + adjustment))
    
    const newHour = Math.floor(newMinutes / 60)
    const newMinute = newMinutes % 60
    const newTimeString = `${newHour.toString().padStart(2, '0')}:${newMinute.toString().padStart(2, '0')}`
    
    if (timeType === 'start') {
      setStartTime(newTimeString)
    } else {
      setEndTime(newTimeString)
    }
  }

  // Render form mode with validation
  if (mode === 'form') {
    return (
      <Card className={cn("w-full max-w-2xl mx-auto bg-white shadow-sm border border-gray-200", className)}>
        <CardHeader className="pb-4">
          <CardTitle className="flex items-center space-x-2 text-lg font-semibold text-gray-900">
            <Clock className="h-5 w-5 text-blue-600" />
            <span>{title}</span>
          </CardTitle>
          {description && (
            <p className="text-sm text-gray-600 mt-1">{description}</p>
          )}
        </CardHeader>

        <CardContent className="pt-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Title Field */}
              {allowCustomTitle && (
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700">
                        Title
                      </FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter time slot title"
                          className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}

              {/* Time Selection */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Start Time */}
                <FormField
                  control={form.control}
                  name="startTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700">
                        Start Time
                      </FormLabel>
                      <FormControl>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {timeOptions.map((time) => (
                              <SelectItem key={time} value={time}>
                                {time}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* End Time */}
                <FormField
                  control={form.control}
                  name="endTime"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="text-sm font-medium text-gray-700">
                        End Time
                      </FormLabel>
                      <FormControl>
                        <Select value={field.value} onValueChange={field.onChange}>
                          <SelectTrigger className="border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            {timeOptions.map((time) => (
                              <SelectItem key={time} value={time}>
                                {time}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Description Field */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium text-gray-700">
                      Description (Optional)
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Enter description"
                        className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Duration Display */}
              {watchedValues.startTime && watchedValues.endTime && (
                <div className="pt-4 border-t border-gray-100">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Duration:</span>
                    <span className="text-sm font-medium text-blue-600">
                      {(() => {
                        const start = new Date(`2000-01-01T${watchedValues.startTime}:00`)
                        const end = new Date(`2000-01-01T${watchedValues.endTime}:00`)
                        const diffMinutes = (end.getTime() - start.getTime()) / (1000 * 60)
                        return formatDuration(diffMinutes)
                      })()}
                    </span>
                  </div>
                </div>
              )}

              {/* Action Buttons */}
              {showActions && (
                <div className="flex justify-end space-x-3 pt-6 border-t border-gray-100">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleCancel}
                    disabled={loading}
                    className="border-gray-300 hover:bg-gray-50"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    disabled={loading || !form.formState.isValid}
                    className="bg-blue-600 hover:bg-blue-700"
                  >
                    {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    <Save className="h-4 w-4 mr-2" />
                    Save
                  </Button>
                </div>
              )}
            </form>
          </Form>
        </CardContent>
      </Card>
    )
  }

  // Render inline mode (legacy)
  return (
    <Card className={cn('bg-white border-blue-200 shadow-lg', className)}>
      <CardHeader className="pb-4 border-b border-gray-100">
        <CardTitle className="text-lg text-gray-900 flex items-center">
          <Clock className="h-5 w-5 mr-2 text-blue-600" />
          {title}
        </CardTitle>
        {description && (
          <p className="text-sm text-gray-600 mt-1">{description}</p>
        )}
      </CardHeader>

      <CardContent className="pt-6 space-y-6">
        {/* Custom Title Input */}
        {allowCustomTitle && (
          <div className="space-y-2">
            <Label htmlFor="slot-title" className="text-sm font-medium text-gray-700">
              Title (Optional)
            </Label>
            <Input
              id="slot-title"
              value={slotTitle}
              onChange={(e) => setSlotTitle(e.target.value)}
              placeholder="Enter time slot title"
              className="border-gray-300 focus:border-blue-500 focus:ring-blue-500"
            />
          </div>
        )}

        {/* Time Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Start Time */}
          <div className="space-y-3">
            <Label className="text-sm font-medium text-gray-700">Start Time</Label>
            <div className="flex items-center space-x-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => adjustTime('start', -step)}
                className="h-9 w-9 p-0 border-gray-300 hover:bg-blue-50"
              >
                <Minus className="h-4 w-4" />
              </Button>
              
              <Select value={startTime} onValueChange={setStartTime}>
                <SelectTrigger className="flex-1 border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timeOptions.map((time) => (
                    <SelectItem key={time} value={time}>
                      {time}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => adjustTime('start', step)}
                className="h-9 w-9 p-0 border-gray-300 hover:bg-blue-50"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* End Time */}
          <div className="space-y-3">
            <Label className="text-sm font-medium text-gray-700">End Time</Label>
            <div className="flex items-center space-x-2">
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => adjustTime('end', -step)}
                className="h-9 w-9 p-0 border-gray-300 hover:bg-blue-50"
              >
                <Minus className="h-4 w-4" />
              </Button>
              
              <Select value={endTime} onValueChange={setEndTime}>
                <SelectTrigger className="flex-1 border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {timeOptions.map((time) => (
                    <SelectItem key={time} value={time}>
                      {time}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => adjustTime('end', step)}
                className="h-9 w-9 p-0 border-gray-300 hover:bg-blue-50"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Duration Display and Validation */}
        <div className="pt-4 border-t border-gray-100">
          {isValidTimeRange() ? (
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-600">Duration:</span>
              <span className="text-sm font-medium text-blue-600">
                {formatDuration(getDuration())}
              </span>
            </div>
          ) : (
            <div className="text-sm text-red-600 bg-red-50 p-3 rounded-lg border border-red-200">
              ⚠️ End time must be after start time
            </div>
          )}
        </div>

        {/* Quick Duration Presets */}
        <div className="space-y-3">
          <Label className="text-sm font-medium text-gray-700">Quick Durations</Label>
          <div className="flex flex-wrap gap-2">
            {[30, 60, 90, 120, 180].map((minutes) => (
              <Button
                key={minutes}
                type="button"
                variant="outline"
                size="sm"
                onClick={() => {
                  const [hour, minute] = startTime.split(':').map(Number)
                  const startMinutes = hour * 60 + minute
                  const endMinutes = startMinutes + minutes
                  const endHour = Math.floor(endMinutes / 60)
                  const endMinute = endMinutes % 60
                  setEndTime(`${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`)
                }}
                className="border-gray-300 hover:bg-blue-50 hover:border-blue-300"
              >
                {formatDuration(minutes)}
              </Button>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
