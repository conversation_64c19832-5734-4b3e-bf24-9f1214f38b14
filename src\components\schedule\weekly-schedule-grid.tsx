'use client'

import * as React from 'react'
import { <PERSON>, Card<PERSON>ontent, CardH<PERSON>er, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'

import { cn } from '@/lib/utils'
import { ChevronLeft, ChevronRight, Clock, Plus, Edit, Trash2 } from 'lucide-react'

interface TimeSlot {
  id: string
  startTime: string
  endTime: string
  title: string
  type: 'working' | 'break' | 'blocked' | 'appointment'
  color?: string
  description?: string
}

interface DaySchedule {
  date: Date
  dayOfWeek: number
  timeSlots: TimeSlot[]
  isWorkingDay: boolean
}

interface WeeklyScheduleGridProps {
  weekStart: Date
  schedule: DaySchedule[]
  onTimeSlotClick?: (timeSlot: TimeSlot, date: Date) => void
  onAddTimeSlot?: (date: Date, time: string) => void
  onEditTimeSlot?: (timeSlot: TimeSlot) => void
  onDeleteTimeSlot?: (timeSlot: TimeSlot) => void
  className?: string
  readOnly?: boolean
}

const DAYS_OF_WEEK = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
const TIME_SLOTS = Array.from({ length: 24 }, (_, i) => {
  const hour = i.toString().padStart(2, '0')
  return `${hour}:00`
})

export function WeeklyScheduleGrid({
  weekStart,
  schedule,
  onTimeSlotClick,
  onAddTimeSlot,
  onEditTimeSlot,
  onDeleteTimeSlot,
  className,
  readOnly = false
}: WeeklyScheduleGridProps) {
  const [currentWeek, setCurrentWeek] = React.useState(weekStart)

  const navigateWeek = (direction: 'prev' | 'next') => {
    setCurrentWeek(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setDate(prev.getDate() - 7)
      } else {
        newDate.setDate(prev.getDate() + 7)
      }
      return newDate
    })
  }

  const getWeekDates = () => {
    const dates = []
    const startOfWeek = new Date(currentWeek)
    startOfWeek.setDate(currentWeek.getDate() - currentWeek.getDay())
    
    for (let i = 0; i < 7; i++) {
      const date = new Date(startOfWeek)
      date.setDate(startOfWeek.getDate() + i)
      dates.push(date)
    }
    return dates
  }

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      timeZone: 'Asia/Manila'
    }).format(date)
  }

  const getTimeSlotColor = (type: TimeSlot['type']) => {
    switch (type) {
      case 'working':
        return 'bg-blue-100 border-blue-300 text-blue-900'
      case 'break':
        return 'bg-green-100 border-green-300 text-green-900'
      case 'blocked':
        return 'bg-red-100 border-red-300 text-red-900'
      case 'appointment':
        return 'bg-purple-100 border-purple-300 text-purple-900'
      default:
        return 'bg-gray-100 border-gray-300 text-gray-900'
    }
  }

  const weekDates = getWeekDates()

  return (
    <Card className={cn('bg-white border-blue-200 shadow-lg', className)}>
      <CardHeader className="pb-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <CardTitle className="text-xl text-gray-900 flex items-center">
            <Clock className="h-6 w-6 mr-3 text-blue-600" />
            Weekly Schedule
          </CardTitle>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateWeek('prev')}
              className="h-9 w-9 p-0 border-blue-200 hover:bg-blue-50 hover:border-blue-300"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <span className="text-sm font-medium text-gray-700 min-w-[120px] text-center">
              {formatDate(weekDates[0])} - {formatDate(weekDates[6])}
            </span>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => navigateWeek('next')}
              className="h-9 w-9 p-0 border-blue-200 hover:bg-blue-50 hover:border-blue-300"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <div className="overflow-x-auto">
          {/* Header Row */}
          <div className="grid grid-cols-8 border-b border-gray-200 bg-gray-50">
            <div className="p-3 text-sm font-medium text-gray-600 border-r border-gray-200">
              Time
            </div>
            {weekDates.map((date, index) => (
              <div
                key={index}
                className="p-3 text-center border-r border-gray-200 last:border-r-0"
              >
                <div className="text-sm font-medium text-gray-900">
                  {DAYS_OF_WEEK[date.getDay()]}
                </div>
                <div className="text-xs text-gray-600 mt-1">
                  {formatDate(date)}
                </div>
              </div>
            ))}
          </div>

          {/* Time Grid */}
          <div className="max-h-96 overflow-y-auto">
            {TIME_SLOTS.slice(6, 20).map((time) => (
              <div key={time} className="grid grid-cols-8 border-b border-gray-100 hover:bg-gray-50">
                <div className="p-2 text-xs text-gray-600 border-r border-gray-200 bg-gray-50">
                  {time}
                </div>
                {weekDates.map((date, dayIndex) => {
                  const daySchedule = schedule.find(s => 
                    s.date.toDateString() === date.toDateString()
                  )
                  const timeSlot = daySchedule?.timeSlots.find(slot => 
                    slot.startTime <= time && slot.endTime > time
                  )

                  return (
                    <div
                      key={`${dayIndex}-${time}`}
                      className="p-1 border-r border-gray-200 last:border-r-0 min-h-[40px] relative group"
                    >
                      {timeSlot ? (
                        <div
                          className={cn(
                            'rounded px-2 py-1 text-xs font-medium border cursor-pointer transition-all duration-200 hover:shadow-sm',
                            getTimeSlotColor(timeSlot.type)
                          )}
                          onClick={() => onTimeSlotClick?.(timeSlot, date)}
                        >
                          <div className="truncate">{timeSlot.title}</div>
                          {!readOnly && (
                            <div className="absolute top-1 right-1 opacity-0 group-hover:opacity-100 transition-opacity">
                              <div className="flex space-x-1">
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-4 w-4 p-0 hover:bg-white/80"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    onEditTimeSlot?.(timeSlot)
                                  }}
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  className="h-4 w-4 p-0 hover:bg-white/80 text-red-600"
                                  onClick={(e) => {
                                    e.stopPropagation()
                                    onDeleteTimeSlot?.(timeSlot)
                                  }}
                                >
                                  <Trash2 className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                          )}
                        </div>
                      ) : (
                        !readOnly && (
                          <Button
                            size="sm"
                            variant="ghost"
                            className="w-full h-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-blue-50"
                            onClick={() => onAddTimeSlot?.(date, time)}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        )
                      )}
                    </div>
                  )
                })}
              </div>
            ))}
          </div>
        </div>

        {/* Legend */}
        <div className="p-4 border-t border-gray-200 bg-gray-50">
          <div className="flex flex-wrap gap-4 text-xs">
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded bg-blue-100 border border-blue-300"></div>
              <span className="text-gray-600">Working Hours</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded bg-green-100 border border-green-300"></div>
              <span className="text-gray-600">Break Time</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded bg-red-100 border border-red-300"></div>
              <span className="text-gray-600">Blocked Time</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-3 h-3 rounded bg-purple-100 border border-purple-300"></div>
              <span className="text-gray-600">Appointments</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
